name: <PERSON><PERSON> Kernel - Android 12
on:
  push:
    branches: [ "next" ]
    paths:
      - ".github/workflows/build-kernel-a12.yml"
      - ".github/workflows/gki-kernel.yml"
      - ".github/scripts/build_a12.sh"
      - "kernel/**"
  pull_request:
    branches: [ "next" ]
    paths:
      - ".github/workflows/build-kernel-a12.yml"
      - ".github/workflows/gki-kernel.yml"
      - ".github/scripts/build-a12.sh"
      - "kernel/**"
  workflow_call:
jobs:
  build-kernel:
    if: github.event_name != 'pull_request' && github.ref != 'refs/heads/next'
    strategy:
      matrix:
        include:
          - sub_level: 218
            os_patch_level: 2024-08
          - sub_level: 226
            os_patch_level: 2024-11
          - sub_level: 233
            os_patch_level: 2025-02
          - sub_level: 236
            os_patch_level: 2025-05
    uses: ./.github/workflows/gki-kernel.yml
    secrets: inherit
    with:
      version: android12-5.10
      version_name: android12-5.10.${{ matrix.sub_level }}
      tag: android12-5.10-${{ matrix.os_patch_level }}
      os_patch_level: ${{ matrix.os_patch_level }}
      patch_path: "5.10"
  
  upload-artifacts:
    needs: build-kernel
    runs-on: ubuntu-latest
    if: ${{ ( github.event_name != 'pull_request' && github.ref == 'refs/heads/next' ) || github.ref_type == 'tag' || github.ref == 'refs/heads/next' }}
    env:
      CHAT_ID: ${{ secrets.CHAT_ID }}
      BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
      MESSAGE_THREAD_ID: ${{ secrets.MESSAGE_THREAD_ID }}
      COMMIT_MESSAGE: ${{ github.event.head_commit.message }}
      COMMIT_URL: ${{ github.event.head_commit.url }}
      RUN_URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4

      - uses: actions/checkout@v4
        with:
          path: KernelSU-Next
          fetch-depth: 0

      - name: List artifacts
        run: |
          tree

      - name: Download prebuilt toolchain
        run: |
          AOSP_MIRROR=https://android.googlesource.com
          BRANCH=main-kernel-build-2024
          git clone $AOSP_MIRROR/platform/prebuilts/build-tools -b $BRANCH --depth 1 build-tools
          git clone $AOSP_MIRROR/kernel/prebuilts/build-tools -b $BRANCH --depth 1 kernel-build-tools
          git clone $AOSP_MIRROR/platform/system/tools/mkbootimg -b $BRANCH --depth 1
          pip3 install telethon

      - name: Set boot sign key
        env:
          BOOT_SIGN_KEY: ${{ secrets.BOOT_SIGN_KEY }}
        run: |
          if [ ! -z "$BOOT_SIGN_KEY" ]; then
            echo "$BOOT_SIGN_KEY" > ./kernel-build-tools/linux-x86/share/avb/testkey_rsa2048.pem
          fi

      - name: Bot session cache
        id: bot_session_cache
        uses: actions/cache@v4
        if: false
        with:
          path: scripts/ksunextbot.session
          key: ${{ runner.os }}-bot-session

      - name: Build boot images
        run: |
          export AVBTOOL=$GITHUB_WORKSPACE/kernel-build-tools/linux-x86/bin/avbtool
          export GZIP=$GITHUB_WORKSPACE/build-tools/path/linux-x86/gzip
          export LZ4=$GITHUB_WORKSPACE/build-tools/path/linux-x86/lz4
          export MKBOOTIMG=$GITHUB_WORKSPACE/mkbootimg/mkbootimg.py
          export UNPACK_BOOTIMG=$GITHUB_WORKSPACE/mkbootimg/unpack_bootimg.py
          cd $GITHUB_WORKSPACE/KernelSU-Next
          export VERSION=$(($(git rev-list --count HEAD) + 10200))
          echo "VERSION: $VERSION"
          cd -
          bash $GITHUB_WORKSPACE/KernelSU-Next/.github/scripts/build_a12.sh

      - name: Display structure of boot files
        run: ls -R

      - name: Upload images artifact
        uses: actions/upload-artifact@v4
        with:
          name: boot-images-android12
          path: Image-android12*/*.img.gz

  check-build-kernel:
    if: (github.event_name == 'pull_request' && !github.event.pull_request.draft) || github.ref == 'refs/heads/next'
    uses: ./.github/workflows/gki-kernel.yml
    with:
      version: android12-5.10
      version_name: android12-5.10.236
      tag: android12-5.10-2025-05
      os_patch_level: 2025-05
      patch_path: "5.10"