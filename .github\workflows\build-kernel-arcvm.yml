name: Build Kernel - ChromeOS ARCVM
on:
  push:
    branches: ["next"]
    paths:
      - ".github/workflows/build-kernel-arcvm.yml"
      - "kernel/**"
  pull_request:
    branches: ["next"]
    paths:
      - ".github/workflows/build-kernel-arcvm.yml"
      - "kernel/**"
  workflow_call:
  workflow_dispatch:

env:
  git_tag: chromeos-5.10-arcvm

jobs:
  build:
    if: github.event_name != 'pull_request' || (github.event_name == 'pull_request' && !github.event.pull_request.draft)
    strategy:
      matrix:
        include:
          - arch: x86_64
            kernel_image_name: bzImage
            build_config: build.config.gki.x86_64
            defconfig: x86_64_arcvm_defconfig
          - arch: arm64
            kernel_image_name: Image
            build_config: build.config.gki.aarch64
            defconfig: arm64_arcvm_defconfig

    name: Build ChromeOS ARCVM kernel
    runs-on: ubuntu-22.04
    env:
      LTO: thin
      ROOT_DIR: /
      KERNEL_DIR: ${{ github.workspace }}/kernel

    steps:
      - name: Install Build Tools
        run: |
          sudo apt-get update
          sudo apt-get install -y --no-install-recommends bc \
              bison build-essential ca-certificates flex git gnupg \
              libelf-dev libssl-dev lsb-release software-properties-common wget \
              libncurses-dev binutils-aarch64-linux-gnu gcc-aarch64-linux-gnu nuget gzip \
              rsync python3 device-tree-compiler

          sudo ln -s --force python3 /usr/bin/python

          export LLVM_VERSION=14
          wget https://apt.llvm.org/llvm.sh
          chmod +x llvm.sh
          sudo ./llvm.sh $LLVM_VERSION
          rm ./llvm.sh
          sudo ln -s --force /usr/bin/clang-$LLVM_VERSION /usr/bin/clang
          sudo ln -s --force /usr/bin/ld.lld-$LLVM_VERSION /usr/bin/ld.lld
          sudo ln -s --force /usr/bin/llvm-objdump-$LLVM_VERSION /usr/bin/llvm-objdump
          sudo ln -s --force /usr/bin/llvm-ar-$LLVM_VERSION /usr/bin/llvm-ar
          sudo ln -s --force /usr/bin/llvm-nm-$LLVM_VERSION /usr/bin/llvm-nm
          sudo ln -s --force /usr/bin/llvm-strip-$LLVM_VERSION /usr/bin/llvm-strip
          sudo ln -s --force /usr/bin/llvm-objcopy-$LLVM_VERSION /usr/bin/llvm-objcopy
          sudo ln -s --force /usr/bin/llvm-readelf-$LLVM_VERSION /usr/bin/llvm-readelf
          sudo ln -s --force /usr/bin/clang++-$LLVM_VERSION /usr/bin/clang++

      - name: Checkout KernelSU-Next
        uses: actions/checkout@v4
        with:
          path: KernelSU-Next
          fetch-depth: 0

      - name: Setup kernel source
        run: git clone https://chromium.googlesource.com/chromiumos/third_party/kernel.git -b ${{ env.git_tag }} --depth=1

      - name: Extract version from Makefile
        working-directory: kernel
        run: |
          VERSION=$(grep -E '^VERSION = ' Makefile | awk '{print $3}')
          PATCHLEVEL=$(grep -E '^PATCHLEVEL = ' Makefile | awk '{print $3}')
          SUBLEVEL=$(grep -E '^SUBLEVEL = ' Makefile | awk '{print $3}')
          echo "ChromeOS ARCVM Linux kernel version: $VERSION.$PATCHLEVEL.$SUBLEVEL"
          echo "version=$VERSION.$PATCHLEVEL.$SUBLEVEL" >> $GITHUB_ENV

      - name: Setup KernelSU-Next
        working-directory: kernel
        run: |
          echo "[+] KernelSU-Next setup"
          KERNEL_ROOT=$GITHUB_WORKSPACE/kernel
          echo "[+] KERNEL_ROOT: $KERNEL_ROOT"
          echo "[+] Copy KernelSU-Next driver to $KERNEL_ROOT/drivers"
          ln -sf $GITHUB_WORKSPACE/KernelSU-Next/kernel $KERNEL_ROOT/drivers/kernelsu-next

          echo "[+] Add KernelSU-Next driver to Makefile"
          DRIVER_MAKEFILE=$KERNEL_ROOT/drivers/Makefile
          DRIVER_KCONFIG=$KERNEL_ROOT/drivers/Kconfig
          grep -q "kernelsu-next" "$DRIVER_MAKEFILE" || printf "\nobj-\$(CONFIG_KSU) += kernelsu-next/\n" >> "$DRIVER_MAKEFILE"
          grep -q "kernelsu-next" "$DRIVER_KCONFIG" || sed -i "/endmenu/i\\source \"drivers/kernelsu-next/Kconfig\"" "$DRIVER_KCONFIG"

          echo "[+] Apply KernelSU patches"
          cd $KERNEL_ROOT && git apply $GITHUB_WORKSPACE/KernelSU-Next/.github/patches/5.10/*.patch || echo "[-] No patch found"

          echo "[+] Patch script/setlocalversion"
          sed -i 's/-dirty//g' $KERNEL_ROOT/scripts/setlocalversion

          echo "[+] KernelSU-Next setup done."
          cd $GITHUB_WORKSPACE/KernelSU-Next
          KSU_VERSION=$(($(git rev-list --count HEAD) + 10200))
          echo "KernelSU-Next version: $KSU_VERSION"
          echo "kernelsu-next_version=$KSU_VERSION" >> $GITHUB_ENV

      - name: Build Kernel
        working-directory: kernel
        env:
          KERNEL_IMAGE_NAME: ${{ matrix.kernel_image_name }}
          ARCH: ${{ matrix.arch }}
        run: |
          set -a && . ${{ matrix.build_config }}; set +a
          export DEFCONFIG=${{ matrix.defconfig }}
          if [ ! -z ${{ vars.EXPECTED_SIZE }} ] && [ ! -z ${{ vars.EXPECTED_HASH }} ]; then
            export KSU_EXPECTED_SIZE=${{ vars.EXPECTED_SIZE }}
            export KSU_EXPECTED_HASH=${{ vars.EXPECTED_HASH }}
          fi

          make LLVM=1 LLVM_IAS=1 DEPMOD=depmod DTC=dtc O=${PWD} mrproper
          make LLVM=1 LLVM_IAS=1 DEPMOD=depmod DTC=dtc O=${PWD} ${DEFCONFIG} < /dev/null
          scripts/config --file .config -e LTO_CLANG -d LTO_NONE -e LTO_CLANG_THIN -d LTO_CLANG_FULL -e THINLTO
          make LLVM=1 LLVM_IAS=1 DEPMOD=depmod DTC=dtc O=${PWD} -j$(nproc) ${KERNEL_IMAGE_NAME} modules prepare-objtool
          ls -l -h ${PWD}/arch/${ARCH}/boot
          echo "file_path=${PWD}/arch/${ARCH}/boot/${KERNEL_IMAGE_NAME}" >> $GITHUB_ENV

      - name: Upload kernel-ARCVM-${{ matrix.arch }}-${{ env.version }}
        uses: actions/upload-artifact@v4
        with:
          name: kernel-ARCVM-${{ matrix.arch }}-${{ env.version }}
          path: "${{ env.file_path }}"