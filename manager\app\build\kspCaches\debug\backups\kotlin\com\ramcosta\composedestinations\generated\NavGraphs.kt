package com.ramcosta.composedestinations.generated

import com.ramcosta.composedestinations.annotation.RootGraph
import com.ramcosta.composedestinations.generated.destinations.*
import com.ramcosta.composedestinations.generated.navgraphs.*
import com.ramcosta.composedestinations.spec.*
import com.rifsxd.ksunext.ui.screen.AppProfileScreen
import com.rifsxd.ksunext.ui.screen.ExecuteModuleActionScreen
import com.rifsxd.ksunext.ui.screen.FlashScreen
import com.rifsxd.ksunext.ui.screen.HomeScreen
import com.rifsxd.ksunext.ui.screen.InstallScreen
import com.rifsxd.ksunext.ui.screen.ModuleScreen
import com.rifsxd.ksunext.ui.screen.SuperUserScreen

/**
 * Class generated if any Composable is annotated with `@Destination`.
 * It aggregates all [DestinationSpec]s in their [NavGraphSpec]s.
 *
 * -------------------------------------------------------
 * **Legend:**                                           
 * * 🗺️: Navigation graph                              
 * * 📍: Destination                                   
 * * 🏁: Marks 🗺️/📍as the start of the parent graph   
 * * 🧩: Means 🗺️/📍is generated on external module          
 * -------------------------------------------------------
 *
 * * 🗺️[RootGraph]
 * * ∙∙∙∙∙∙∙∙↳📍🏁[HomeScreen]
 * * ∙∙∙∙∙∙∙∙↳📍[AppProfileScreen]
 * * ∙∙∙∙∙∙∙∙↳📍[ExecuteModuleActionScreen]
 * * ∙∙∙∙∙∙∙∙↳📍[FlashScreen]
 * * ∙∙∙∙∙∙∙∙↳📍[InstallScreen]
 * * ∙∙∙∙∙∙∙∙↳📍[ModuleScreen]
 * * ∙∙∙∙∙∙∙∙↳📍[SuperUserScreen]
 */
internal object NavGraphs {

    val root = RootNavGraph
}