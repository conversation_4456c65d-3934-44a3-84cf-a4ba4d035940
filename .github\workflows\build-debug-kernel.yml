name: Build debug kernel
on:
  workflow_dispatch:

jobs:
  build-debug-kernel-a12:
    uses: ./.github/workflows/gki-kernel.yml
    with:
      version: android12-5.10
      version_name: android12-5.10.236
      tag: android12-5.10-2025-05
      os_patch_level: 2025-05
      patch_path: "5.10"
      debug: true
  build-debug-kernel-a13:
    strategy:
      matrix:
        include:
          - version: "5.10"
            sub_level: 236
            os_patch_level: 2025-05
          - version: "5.15"
            sub_level: 180
            os_patch_level: 2025-05
    uses: ./.github/workflows/gki-kernel.yml
    with:
      version: android13-${{ matrix.version }}
      version_name: android13-${{ matrix.version }}.${{ matrix.sub_level }}
      tag: android13-${{ matrix.version }}-${{ matrix.os_patch_level }}
      patch_path: ${{ matrix.version }}
      debug: true
  build-debug-kernel-a14:
    strategy:
      matrix:
        include:
          - version: "5.15"
            sub_level: 180
            os_patch_level: 2025-05
          - version: "6.1"
            sub_level: 138
            os_patch_level: 2025-06
    uses: ./.github/workflows/gki-kernel.yml
    with:
      version: android14-${{ matrix.version }}
      version_name: android14-${{ matrix.version }}.${{ matrix.sub_level }}
      tag: android14-${{ matrix.version }}-${{ matrix.os_patch_level }}
      patch_path: ${{ matrix.version }}
      debug: true
  build-debug-kernel-a15:
    strategy:
      matrix:
        include:
          - version: "6.6"
            sub_level: 89
            os_patch_level: 2025-06
    uses: ./.github/workflows/gki-kernel.yml
    with:
      version: android15-${{ matrix.version }}
      version_name: android15-${{ matrix.version }}.${{ matrix.sub_level }}
      tag: android15-${{ matrix.version }}-${{ matrix.os_patch_level }}
      patch_path: ${{ matrix.version }}
      debug: true