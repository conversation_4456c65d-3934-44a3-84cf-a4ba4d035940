name: ShellCheck

on:
  push:
    branches:
      - 'next'
    paths:
      - '.github/workflows/shellcheck.yml'
      - '**/*.sh'
  pull_request:
    branches:
      - 'next'
    paths:
      - '.github/workflows/shellcheck.yml'
      - '**/*.sh'

jobs:
  shellcheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Run ShellCheck
        uses: ludeeus/action-shellcheck@2.0.0
        with:
          ignore_names: gradlew
          ignore_paths: ./userspace/ksud_magic/src/installer.sh ./userspace/ksud_overlayfs/src/installer.sh