name: Clippy check

on:
  push:
    branches:
      - next
    paths:
      - '.github/workflows/clippy.yml'
      - 'userspace/ksud_magic/**'
      - 'userspace/ksud_overlayfs/**'
  pull_request:
    branches:
      - next
    paths:
      - '.github/workflows/clippy.yml'
      - 'userspace/ksud_magic/**'
      - 'userspace/ksud_overlayfs/**'

env:
  RUSTFLAGS: '-Dwarnings'

jobs:
  clippy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Rust
        run: rustup update stable

      - name: Setup Cross
        run: RUSTFLAGS="" cargo install cross

      - name: Cache ksud_overlayfs
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: userspace/ksud_overlayfs

      - name: Cache ksud_magic
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: userspace/ksud_magic

      - name: Run Clippy
        run: |
          cross clippy --manifest-path userspace/ksud_magic/Cargo.toml --target aarch64-linux-android --release
          cross clippy --manifest-path userspace/ksud_overlayfs/Cargo.toml --target aarch64-linux-android --release

          cross clippy --manifest-path userspace/ksud_magic/Cargo.toml --target armv7-linux-androideabi --release
          cross clippy --manifest-path userspace/ksud_overlayfs/Cargo.toml --target armv7-linux-androideabi --release

          cross clippy --manifest-path userspace/ksud_magic/Cargo.toml --target x86_64-linux-android --release
          cross clippy --manifest-path userspace/ksud_overlayfs/Cargo.toml --target x86_64-linux-android --release