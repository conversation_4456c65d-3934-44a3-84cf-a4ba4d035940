[{"level_": 0, "message_": "Start JSON generation. Platform version: 26 min SDK version: armeabi-v7a", "file_": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\.cxx\\Debug\\5xa4m1q7\\armeabi-v7a\\android_gradle_build.json' was up-to-date", "file_": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]