{"info": {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, "cxxBuildFolder": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\.cxx\\Debug\\5xa4m1q7\\arm64-v8a", "soFolder": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\build\\intermediates\\cxx\\Debug\\5xa4m1q7\\obj\\arm64-v8a", "soRepublishFolder": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\build\\intermediates\\cmake\\debug\\obj\\arm64-v8a", "abiPlatformVersion": 26, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_STL=none", "-DCMAKE_CXX_FLAGS_DEBUG=-Og", "-DCMAKE_C_FLAGS_DEBUG=-Og"], "cFlagsList": ["-std=c2x", "-Wall", "-Qunused-arguments", "-fno-rtti", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-fno-exceptions", "-fno-stack-protector", "-fomit-frame-pointer", "-Wno-builtin-macro-redefined", "-Wno-unused-value", "-D__FILE__=__FILE_NAME__"], "cppFlagsList": ["-std=c++2b", "-Wall", "-Qunused-arguments", "-fno-rtti", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-fno-exceptions", "-fno-stack-protector", "-fomit-frame-pointer", "-Wno-builtin-macro-redefined", "-Wno-unused-value", "-D__FILE__=__FILE_NAME__"], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\.cxx", "intermediatesBaseFolder": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\build\\intermediates", "intermediatesFolder": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\build\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app", "moduleBuildFile": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\build.gradle.kts", "makeFile": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\src\\main\\cpp\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Android\\Sdk\\ndk\\28.1.13356709", "ndkFolderBeforeSymLinking": "C:\\Android\\Sdk\\ndk\\28.1.13356709", "ndkVersion": "28.1.13356709", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "C:\\Android\\Sdk\\ndk\\28.1.13356709\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Android\\SDK\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "C:\\Android\\Sdk\\ndk\\28.1.13356709\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "C:\\Android\\Sdk\\ndk\\28.1.13356709\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "riscv64": "C:\\Android\\Sdk\\ndk\\28.1.13356709\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\riscv64-linux-android\\libc++_shared.so", "x86": "C:\\Android\\Sdk\\ndk\\28.1.13356709\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "C:\\Android\\Sdk\\ndk\\28.1.13356709\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager", "sdkFolder": "C:\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "C:\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": ["C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar"], "prefabPackages": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\290d87afc4f3d4d5661561da4a4f63ee\\transformed\\libcxx-28.1.13356709\\prefab"], "prefabPackageConfigurations": [], "stlType": "none", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\.cxx\\Debug\\5xa4m1q7\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "5xa4m1q74x3m1b116d4r3z2n4p3p3t4a631n6p3g4o194k55584l4g2b4o252m", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.10.1.\n#   - $NDK is the path to NDK 28.1.13356709.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H$PROJECT/app/src/main/cpp\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=26\n-DANDROID_PLATFORM=android-26\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_C_FLAGS=-std=c2x -Wall -Qunused-arguments -fno-rtti -fvisibility=hidden -fvisibility-inlines-hidden -fno-exceptions -fno-stack-protector -fomit-frame-pointer -Wno-builtin-macro-redefined -Wno-unused-value -D__FILE__=__FILE_NAME__\n-DCMAKE_CXX_FLAGS=-std=c++2b -Wall -Qunused-arguments -fno-rtti -fvisibility=hidden -fvisibility-inlines-hidden -fno-exceptions -fno-stack-protector -fomit-frame-pointer -Wno-builtin-macro-redefined -Wno-unused-value -D__FILE__=__FILE_NAME__\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-DCMAKE_FIND_ROOT_PATH=$PROJECT/app/.cxx/Debug/$HASH/prefab/$ABI/prefab\n-B$PROJECT/app/.cxx/Debug/$HASH/$ABI\n-GNinja\n-DANDROID_STL=none\n-DCMAKE_CXX_FLAGS_DEBUG=-Og\n-DCMAKE_C_FLAGS_DEBUG=-Og", "configurationArguments": ["-HC:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\src\\main\\cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=26", "-DANDROID_PLATFORM=android-26", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=C:\\Android\\Sdk\\ndk\\28.1.13356709", "-DCMAKE_ANDROID_NDK=C:\\Android\\Sdk\\ndk\\28.1.13356709", "-DCMAKE_TOOLCHAIN_FILE=C:\\Android\\Sdk\\ndk\\28.1.13356709\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_C_FLAGS=-std=c2x -Wall -Qunused-arguments -fno-rtti -fvisibility=hidden -fvisibility-inlines-hidden -fno-exceptions -fno-stack-protector -fomit-frame-pointer -Wno-builtin-macro-redefined -Wno-unused-value -D__FILE__=__FILE_NAME__", "-DCMAKE_CXX_FLAGS=-std=c++2b -Wall -Qunused-arguments -fno-rtti -fvisibility=hidden -fvisibility-inlines-hidden -fno-exceptions -fno-stack-protector -fomit-frame-pointer -Wno-builtin-macro-redefined -Wno-unused-value -D__FILE__=__FILE_NAME__", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\build\\intermediates\\cxx\\Debug\\5xa4m1q7\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\build\\intermediates\\cxx\\Debug\\5xa4m1q7\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_FIND_ROOT_PATH=C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\.cxx\\Debug\\5xa4m1q7\\prefab\\arm64-v8a\\prefab", "-BC:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\.cxx\\Debug\\5xa4m1q7\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_STL=none", "-DCMAKE_CXX_FLAGS_DEBUG=-Og", "-DCMAKE_C_FLAGS_DEBUG=-Og"], "intermediatesParentFolder": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\build\\intermediates\\cxx\\Debug\\5xa4m1q7"}