package com.rifsxd.ksunext.ui.theme

import androidx.compose.ui.graphics.Color

// 自定义蓝色主题配色
val PRIMARY_BLUE = Color(0xFF415F91)           // 主蓝色：深蓝色
val LIGHT_BLUE_CONTAINER = Color(0xFFD6E3FF)   // 浅蓝色容器：非常浅的蓝色
val DARK_BLUE_TEXT = Color(0xFF284777)         // 深蓝色文字：更深的蓝色
val SECONDARY_BLUE_GREY = Color(0xFF565F71)    // 次要蓝灰色：蓝灰色
val LIGHT_BLUE_GREY_CONTAINER = Color(0xFFDAE2F9) // 浅蓝灰色容器：浅蓝灰色

val AMOLED_BLACK = Color(0xFF000000)      // Pure black for AMOLED

val GREEN = Color(0xFF4CAF50)             // Green
val RED = Color(0xFFF44336)               // Red
val YELLOW = Color(0xFFFFEB3B)            // Yellow
val ORANGE = Color(0xFFFF9800)            // Orange