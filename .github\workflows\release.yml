name: Release
on:
  push:
    tags:
      - "v*"
  workflow_dispatch:

jobs:
  build-manager:
    uses: ./.github/workflows/build-manager.yml
    secrets: inherit
  build-a12-kernel:
    uses: ./.github/workflows/build-kernel-a12.yml
    secrets: inherit
  build-a13-kernel:
    uses: ./.github/workflows/build-kernel-a13.yml
    secrets: inherit
  build-a14-kernel:
    uses: ./.github/workflows/build-kernel-a14.yml
    secrets: inherit
  build-a15-kernel:
    uses: ./.github/workflows/build-kernel-a15.yml
    secrets: inherit
  build-wsa-kernel:
    uses: ./.github/workflows/build-kernel-wsa.yml
    secrets: inherit
  build-arcvm-kernel:
    uses: ./.github/workflows/build-kernel-arcvm.yml
    secrets: inherit
  release:
    needs:
      - build-manager
      - build-a12-kernel
      - build-a13-kernel
      - build-a14-kernel
      - build-a15-kernel
      - build-wsa-kernel
      - build-arcvm-kernel
    runs-on: ubuntu-latest
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4
      - name: Zip AnyKernel3
        run: |
          for dir in AnyKernel3-*; do
            if [ -d "$dir" ]; then
              echo "----- Zip $dir -----"
              (cd $dir && zip -r9 "$dir".zip ./* -x .git .gitignore ./*.zip && mv *.zip ..)
            fi
          done

      - name: Zip WSA kernel
        run: |
          for dir in kernel-WSA-*; do
            if [ -d "$dir" ]; then
              echo "------ Zip $dir ----------"
              (cd $dir && zip -r9 "$dir".zip ./* -x .git .gitignore ./*.zip && mv *.zip ..)
            fi
          done

      - name: Zip ChromeOS ARCVM kernel
        run: |
          for dir in kernel-ARCVM-*; do
            if [ -d "$dir" ]; then
              echo "------ Zip $dir ----------"
              (cd $dir && zip -r9 "$dir".zip ./* -x .git .gitignore ./*.zip && mv *.zip ..)
            fi
          done

      - name: Display structure of downloaded files
        run: ls -R

      - name: release
        uses: softprops/action-gh-release@v2
        with:
          files: |
            Manager/*.apk
            android*-lkm/*_kernelsu.ko
            AnyKernel3-*.zip
            boot-images-*/Image-*/*.img.gz
            kernel-WSA*.zip
            kernel-ARCVM*.zip
            ksud_magic*.zip
            ksud_overlayfs*.zip
            susfsd*.zip