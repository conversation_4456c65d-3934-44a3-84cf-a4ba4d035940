name: Build SU
on:
  push:
    branches: [ "next" ]
    paths:
      - '.github/workflows/build-su.yml'
      - 'userspace/su/**'
      - 'scripts/ksunextbot.py'
  pull_request:
    branches: [ "next" ]
    paths:
      - 'userspace/su/**'
jobs:
  build-su:
    name: Build userspace su
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Setup need_upload
      id: need_upload
      run: |
        if [ ! -z "${{ secrets.BOT_TOKEN }}" ]; then
          echo "UPLOAD=true" >> $GITHUB_OUTPUT
        else
          echo "UPLOAD=false" >> $GITHUB_OUTPUT
        fi
    - name: Build su
      working-directory: ./userspace/su
      run: $ANDROID_NDK/ndk-build
    - name: Upload a Build Artifact
      uses: actions/upload-artifact@v4
      with:
        name: su
        path: ./userspace/su/libs
