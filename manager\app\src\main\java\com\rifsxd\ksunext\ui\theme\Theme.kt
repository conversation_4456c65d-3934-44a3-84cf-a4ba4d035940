package com.rifsxd.ksunext.ui.theme

import android.os.Build
import androidx.activity.SystemBarStyle
import androidx.activity.ComponentActivity
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext

// 自定义蓝色浅色主题配色方案
private val CustomLightColorScheme = lightColorScheme(
    primary = PRIMARY_BLUE,                    // 主蓝色
    onPrimary = Color.White,                   // 主色上的文字为白色
    primaryContainer = LIGHT_BLUE_CONTAINER,   // 浅蓝色容器
    onPrimaryContainer = DARK_BLUE_TEXT,       // 容器上的深蓝色文字

    secondary = SECONDARY_BLUE_GREY,           // 次要蓝灰色
    onSecondary = Color.White,                 // 次要色上的文字为白色
    secondaryContainer = LIGHT_BLUE_GREY_CONTAINER, // 浅蓝灰色容器
    onSecondaryContainer = DARK_BLUE_TEXT,     // 容器上的深蓝色文字

    tertiary = PRIMARY_BLUE,                   // 第三色使用主蓝色
    onTertiary = Color.White,
    tertiaryContainer = LIGHT_BLUE_CONTAINER,
    onTertiaryContainer = DARK_BLUE_TEXT,

    background = Color.White,                  // 背景为白色
    onBackground = DARK_BLUE_TEXT,             // 背景上的深蓝色文字
    surface = Color.White,                     // 表面为白色
    onSurface = DARK_BLUE_TEXT,                // 表面上的深蓝色文字

    surfaceVariant = LIGHT_BLUE_GREY_CONTAINER, // 表面变体使用浅蓝灰色
    onSurfaceVariant = SECONDARY_BLUE_GREY,     // 表面变体上的蓝灰色文字

    outline = SECONDARY_BLUE_GREY,              // 轮廓使用蓝灰色
    outlineVariant = LIGHT_BLUE_GREY_CONTAINER  // 轮廓变体使用浅蓝灰色
)

fun Color.blend(other: Color, ratio: Float): Color {
    val inverse = 1f - ratio
    return Color(
        red = red * inverse + other.red * ratio,
        green = green * inverse + other.green * ratio,
        blue = blue * inverse + other.blue * ratio,
        alpha = alpha
    )
}

@Composable
fun KernelSUTheme(
    darkTheme: Boolean = false, // 强制使用浅色模式
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false, // 禁用动态颜色，使用自定义配色
    amoledMode: Boolean = false,
    content: @Composable () -> Unit
) {
    // 强制使用自定义浅色主题
    val colorScheme = CustomLightColorScheme

    SystemBarStyle(
        darkMode = false // 强制浅色状态栏
    )

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}

@Composable
private fun SystemBarStyle(
    darkMode: Boolean,
    statusBarScrim: Color = Color.Transparent,
    navigationBarScrim: Color = Color.Transparent,
) {
    val context = LocalContext.current
    val activity = context as ComponentActivity

    SideEffect {
        activity.enableEdgeToEdge(
            statusBarStyle = SystemBarStyle.auto(
                statusBarScrim.toArgb(),
                statusBarScrim.toArgb(),
            ) { darkMode },
            navigationBarStyle = when {
                darkMode -> SystemBarStyle.dark(
                    navigationBarScrim.toArgb()
                )

                else -> SystemBarStyle.light(
                    navigationBarScrim.toArgb(),
                    navigationBarScrim.toArgb(),
                )
            }
        )
    }
}