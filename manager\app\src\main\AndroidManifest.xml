<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>

    <application
        android:name=".KernelSUApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:enableOnBackInvokedCallback="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/Theme.KernelSU"
        tools:targetApi="34">
        <activity
            android:name=".ui.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.KernelSU">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:mimeType="application/zip" />
                <data android:scheme="file" />
                <data android:scheme="content" />
                <data android:pathPattern=".*\\.zip" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.webui.WebUIActivity"
            android:autoRemoveFromRecents="true"
            android:documentLaunchMode="intoExisting"
            android:exported="false"
            android:theme="@style/Theme.KernelSU.WebUI" />

        <activity
            android:name=".ui.webui.WebUIXActivity"
            android:autoRemoveFromRecents="true"
            android:documentLaunchMode="intoExisting"
            android:exported="false"
            android:theme="@style/Theme.KernelSU.WebUI" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths" />
        </provider>
    </application>

</manifest>