name: <PERSON><PERSON> Kernel - AVD
on:
  push:
    branches: ["next"]
    paths:
      - ".github/workflows/build-kernel-avd.yml"
      - ".github/workflows/avd-kernel.yml"
      - "kernel/**"
  pull_request:
    branches: ["next"]
    paths:
      - ".github/workflows/build-kernel-avd.yml"
      - ".github/workflows/avd-kernel.yml"
      - "kernel/**"
  workflow_call:
  workflow_dispatch:

jobs:
  build-kernel:
    if: github.event_name != 'pull_request' && github.ref != 'refs/heads/checkci'
    uses: ./.github/workflows/avd-kernel.yml
    secrets: inherit
    strategy:
      matrix:
        include:
          - version: "android-14-avd_x86_64"
            manifest: "android-14-avd_x86_64.xml"
            arch: "x86_64"
          - version: "android-15-avd_aarch64"
            manifest: "android-15-avd_aarch64.xml"
            arch: "aarch64"
          - version: "android-15-avd_x86_64"
            manifest: "android-15-avd_x86_64.xml"
            arch: "x86_64"
    with:
      version_name: ${{ matrix.version }}
      manifest_name: ${{ matrix.manifest }}
      arch: ${{ matrix.arch }}
      debug: true