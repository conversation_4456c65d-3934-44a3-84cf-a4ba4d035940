name: Rustfmt check

on:
  push:
    branches:
      - 'next'
    paths:
      - '.github/workflows/rustfmt.yml'
      - 'userspace/ksud_magic/**'
      - 'userspace/ksud_overlayfs/**'
  pull_request:
    branches:
      - 'next'
    paths:
      - '.github/workflows/rustfmt.yml'
      - 'userspace/ksud_magic/**'
      - 'userspace/ksud_overlayfs/**'

permissions:
  checks: write

jobs:
  format:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: dtolnay/rust-toolchain@nightly
        with:
          components: rustfmt

      - uses: LoliGothick/rustfmt-check@master
        with:
          token: ${{ github.token }}
          working-directory: userspace/ksud_magic

      - uses: LoliGothick/rustfmt-check@master
        with:
          token: ${{ github.token }}
          working-directory: userspace/ksud_overlayfs