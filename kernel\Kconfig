menu "KernelSU"

config <PERSON><PERSON>
	tristate "KernelSU function support"
	depends on OVERLAY_FS
	default y
	help
	  Enable kernel-level root privileges on Android System.
	  To compile as a module, choose M here: the
	  module will be called kernelsu.

config KSU_KPROBES_HOOK
	bool "Use kprobes for kernelsu"
	depends on KSU
	depends on KPROBES
	default y
	help
	  Disable if you use manual hooks.

config KSU_DEBUG
	bool "KernelSU debug mode"
	depends on KSU
	default n
	help
	  Enable KernelSU debug mode.

config KSU_ALLOWLIST_WORKAROUND
        bool "KernelSU Session Keyring Init workaround"
        depends on KSU
        default n
        help
          Enable session keyring init workaround for problematic devices.
          Useful for situations where the SU allowlist is not kept after a reboot.

config KSU_LSM_SECURITY_HOOKS
	bool "use lsm security hooks"
        depends on KSU
        default y
	help
	  Disabling this is mostly only useful for kernel 4.1 and older.
	  Make sure to implement manual hooks on security/security.c.

endmenu
