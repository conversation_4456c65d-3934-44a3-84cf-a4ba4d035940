name: Bug report
description: Create a report to help us improve KernelSU Next
labels: [Bug]

body:
  - type: checkboxes
    attributes:
       label: Please check before submitting an issue
       options:
          - label: I have searched the issues and haven't found anything relevant
            required: true
            
          - label: I will upload bugreport file in KernelSU Manager - Settings - Report log
            required: true
            
          - label: I know how to reproduce the issue which may not be specific to my device
            required: false


  - type: textarea
    attributes:
        label: Describe the bug
        description: A clear and concise description of what the bug is
    validations:
        required: true
      

  - type: textarea
    attributes:
        label: To Reproduce
        description: Steps to reproduce the behaviour
        placeholder: |
          - 1. Go to '...'
          - 2. Click on '....'
          - 3. Scroll down to '....'
          - 4. See error
          

  - type: textarea
    attributes:
        label: Expected behavior
        description: A clear and concise description of what you expected to happen.
    
    
  - type: textarea
    attributes:
        label: Screenshots
        description: If applicable, add screenshots to help explain your problem.
        
        
  - type: textarea
    attributes:
        label: Logs
        description: If applicable, add crash or any other logs to help us figure out the problem.
        
        
  - type: textarea
    attributes:
        label: Device info
        value: |
          - Device:
          - OS Version:
          - Kernel Version:
          - KSUN Driver Version:
          - KSUN Manager Version:
    validations:
        required: true


  - type: textarea
    attributes:
        label: Additional context
        description: Add any other context about the problem here.
