name: <PERSON><PERSON> Kernel - Android 14
on:
  push:
    branches: [ "next" ]
    paths:
      - ".github/workflows/build-kernel-a14.yml"
      - ".github/workflows/gki-kernel.yml"
      - ".github/scripts/build_a13.sh"
      - "kernel/**"
  pull_request:
    branches: [ "next" ]
    paths:
      - ".github/workflows/build-kernel-a14.yml"
      - ".github/workflows/gki-kernel.yml"
      - ".github/scripts/build-a13.sh"
      - "kernel/**"
  workflow_call:
jobs:
  build-kernel:
    if: github.event_name != 'pull_request' && github.ref != 'refs/heads/next'
    strategy:
      matrix:
        include:
          - version: "5.15"
            sub_level: 149
            os_patch_level: 2024-06
          - version: "5.15"
            sub_level: 153
            os_patch_level: 2024-07
          - version: "5.15"
            sub_level: 158
            os_patch_level: 2024-08
          - version: "5.15"
            sub_level: 164
            os_patch_level: 2024-09
          - version: "5.15"
            sub_level: 167
            os_patch_level: 2024-11
          - version: "5.15"
            sub_level: 170
            os_patch_level: 2025-01
          - version: "5.15"
            sub_level: 178
            os_patch_level: 2025-03
          - version: "5.15"
            sub_level: 180
            os_patch_level: 2025-05
          - version: "6.1"
            sub_level: 78
            os_patch_level: 2024-06
          - version: "6.1"
            sub_level: 84
            os_patch_level: 2024-07
          - version: "6.1"
            sub_level: 90
            os_patch_level: 2024-08
          - version: "6.1"
            sub_level: 93
            os_patch_level: 2024-09
          - version: "6.1"
            sub_level: 99
            os_patch_level: 2024-10
          - version: "6.1"
            sub_level: 112
            os_patch_level: 2024-11
          - version: "6.1"
            sub_level: 115
            os_patch_level: 2024-12
          - version: "6.1"
            sub_level: 118
            os_patch_level: 2025-01
          - version: "6.1"
            sub_level: 124
            os_patch_level: 2025-02
          - version: "6.1"
            sub_level: 128
            os_patch_level: 2025-03
          - version: "6.1"
            sub_level: 129
            os_patch_level: 2025-04
          - version: "6.1"
            sub_level: 134
            os_patch_level: 2025-05
          - version: "6.1"
            sub_level: 138
            os_patch_level: 2025-06
    uses: ./.github/workflows/gki-kernel.yml
    secrets: inherit
    with:
      version: android14-${{ matrix.version }}
      version_name: android14-${{ matrix.version }}.${{ matrix.sub_level }}
      tag: android14-${{ matrix.version }}-${{ matrix.os_patch_level }}
      os_patch_level: ${{ matrix.os_patch_level }}
      patch_path: ${{ matrix.version }}
  
  upload-artifacts:
    needs: build-kernel
    runs-on: ubuntu-latest
    if: ${{ ( github.event_name != 'pull_request' && github.ref == 'refs/heads/next' ) || github.ref_type == 'tag' || github.ref == 'refs/heads/next' }}
    env:
      CHAT_ID: ${{ secrets.CHAT_ID }}
      BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
      MESSAGE_THREAD_ID: ${{ secrets.MESSAGE_THREAD_ID }}
      COMMIT_MESSAGE: ${{ github.event.head_commit.message }}
      COMMIT_URL: ${{ github.event.head_commit.url }}
      RUN_URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4

      - uses: actions/checkout@v4
        with:
          path: KernelSU-Next
          fetch-depth: 0

      - name: List artifacts
        run: |
          tree

      - name: Download prebuilt toolchain
        run: |
          AOSP_MIRROR=https://android.googlesource.com
          BRANCH=main-kernel-build-2024
          git clone $AOSP_MIRROR/platform/prebuilts/build-tools -b $BRANCH --depth 1 build-tools
          git clone $AOSP_MIRROR/kernel/prebuilts/build-tools -b $BRANCH --depth 1 kernel-build-tools
          git clone $AOSP_MIRROR/platform/system/tools/mkbootimg -b $BRANCH --depth 1
          pip3 install telethon

      - name: Set boot sign key
        env:
          BOOT_SIGN_KEY: ${{ secrets.BOOT_SIGN_KEY }}
        run: |
          if [ ! -z "$BOOT_SIGN_KEY" ]; then
            echo "$BOOT_SIGN_KEY" > ./kernel-build-tools/linux-x86/share/avb/testkey_rsa2048.pem
          fi

      - name: Bot session cache
        id: bot_session_cache
        uses: actions/cache@v4
        if: false
        with:
          path: scripts/ksunextbot.session
          key: ${{ runner.os }}-bot-session

      - name: Build boot images
        run: |
          export AVBTOOL=$GITHUB_WORKSPACE/kernel-build-tools/linux-x86/bin/avbtool
          export GZIP=$GITHUB_WORKSPACE/build-tools/path/linux-x86/gzip
          export LZ4=$GITHUB_WORKSPACE/build-tools/path/linux-x86/lz4
          export MKBOOTIMG=$GITHUB_WORKSPACE/mkbootimg/mkbootimg.py
          export UNPACK_BOOTIMG=$GITHUB_WORKSPACE/mkbootimg/unpack_bootimg.py
          cd $GITHUB_WORKSPACE/KernelSU-Next
          export VERSION=$(($(git rev-list --count HEAD) + 10200))
          echo "VERSION: $VERSION"
          cd -
          bash $GITHUB_WORKSPACE/KernelSU-Next/.github/scripts/build_a13.sh

      - name: Display structure of boot files
        run: ls -R

      - name: Upload images artifact
        uses: actions/upload-artifact@v4
        with:
          name: boot-images-android14
          path: Image-android14*/*.img.gz

  check-build-kernel:
    if: (github.event_name == 'pull_request' && !github.event.pull_request.draft) || github.ref == 'refs/heads/next'
    strategy:
      matrix:
        include:
          - version: "5.15"
            sub_level: 180
            os_patch_level: 2025-05
          - version: "6.1"
            sub_level: 138
            os_patch_level: 2025-06
    uses: ./.github/workflows/gki-kernel.yml
    with:
      version: android14-${{ matrix.version }}
      version_name: android14-${{ matrix.version }}.${{ matrix.sub_level }}
      tag: android14-${{ matrix.version }}-${{ matrix.os_patch_level }}
      os_patch_level: ${{ matrix.os_patch_level }}
      patch_path: ${{ matrix.version }}