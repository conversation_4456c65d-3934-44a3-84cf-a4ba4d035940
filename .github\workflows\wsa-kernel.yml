name: Build Kernel - WSA
on:
  workflow_call:
    inputs:
      arch:
        required: true
        type: string
        description: >
          Build arch: x86_64 / arm64
      version:
        required: true
        type: string
        description: >
          Build version
jobs:
  build:
    name: Build WSA-Kernel-${{ inputs.version }}-${{ inputs.arch }}
    runs-on: ubuntu-22.04
    env:
      CCACHE_COMPILERCHECK: "%compiler% -dumpmachine; %compiler% -dumpversion"
      CCACHE_NOHASHDIR: "true"
      CCACHE_HARDLINK: "true"

    steps:
      - name: Install Build Tools
        uses: awalsh128/cache-apt-pkgs-action@v1
        with:
          packages: bc bison build-essential flex libelf-dev binutils-aarch64-linux-gnu gcc-aarch64-linux-gnu gzip ccache
          version: 1.0

      - name: Cache LLVM
        id: cache-llvm
        uses: actions/cache@v4
        with:
          path: ./llvm
          key: llvm-12.0.1

      - name: Setup LLVM
        uses: KyleMayes/install-llvm-action@v1
        with:
          version: "12.0.1"
          force-version: true
          ubuntu-version: "16.04"
          cached: ${{ steps.cache-llvm.outputs.cache-hit }}

      - name: Checkout KernelSU-Next
        uses: actions/checkout@v4
        with:
          path: KernelSU-Next
          fetch-depth: 0

      - name: Setup kernel source
        uses: actions/checkout@v4
        with:
          repository: microsoft/WSA-Linux-Kernel
          ref: android-lts/latte-2/${{ inputs.version }}
          path: WSA-Linux-Kernel

      - name: Setup Ccache
        uses: hendrikmuhs/ccache-action@v1
        with:
          key: WSA-Kernel-${{ inputs.version }}-${{ inputs.arch }}
          save: ${{ github.event_name == 'push' && github.ref == 'refs/heads/main' }}
          max-size: 2G

      - name: Setup KernelSU-Next
        working-directory: WSA-Linux-Kernel
        run: |
          echo "[+] KernelSU-Next setup"
          KERNEL_ROOT=$GITHUB_WORKSPACE/WSA-Linux-Kernel
          echo "[+] KERNEL_ROOT: $KERNEL_ROOT"
          echo "[+] Copy KernelSU-Next driver to $KERNEL_ROOT/drivers"
          ln -sf $GITHUB_WORKSPACE/KernelSU-Next/kernel $KERNEL_ROOT/drivers/kernelsu
          echo "[+] Add KernelSU-Next driver to Makefile"
          DRIVER_MAKEFILE=$KERNEL_ROOT/drivers/Makefile
          DRIVER_KCONFIG=$KERNEL_ROOT/drivers/Kconfig
          grep -q "kernelsu" "$DRIVER_MAKEFILE" || printf "\nobj-\$(CONFIG_KSU) += kernelsu/\n" >> "$DRIVER_MAKEFILE"
          grep -q "kernelsu" "$DRIVER_KCONFIG" || sed -i "/endmenu/i\\source \"drivers/kernelsu/Kconfig\"" "$DRIVER_KCONFIG"
          echo "[+] Apply KernelSU-Next patches"
          cd $KERNEL_ROOT && git apply $GITHUB_WORKSPACE/KernelSU-Next/.github/patches/5.15/*.patch || echo "[-] No patch found"
          echo "[+] KernelSU-Next setup done."
          cd $GITHUB_WORKSPACE/KernelSU-Next
          VERSION=$(($(git rev-list --count HEAD) + 10200))
          echo "VERSION: $VERSION"
          echo "kernelsu_version=$VERSION" >> $GITHUB_ENV

      - name: Build Kernel
        working-directory: WSA-Linux-Kernel
        run: |
          if [ ! -z ${{ vars.EXPECTED_SIZE }} ] && [ ! -z ${{ vars.EXPECTED_HASH }} ]; then
            export KSU_EXPECTED_SIZE=${{ vars.EXPECTED_SIZE }}
            export KSU_EXPECTED_HASH=${{ vars.EXPECTED_HASH }}
          fi
          declare -A ARCH_MAP=(["x86_64"]="x64" ["arm64"]="arm64")
          cp configs/wsa/config-wsa-${ARCH_MAP[${{ inputs.arch }}]} .config
          make olddefconfig
          declare -A FILE_NAME=(["x86_64"]="bzImage" ["arm64"]="Image")
          make -j`nproc` LLVM=1 ARCH=${{ inputs.arch }} $(if [ "${{ inputs.arch }}" == "arm64" ]; then echo CROSS_COMPILE=aarch64-linux-gnu; fi) ${FILE_NAME[${{ inputs.arch }}]} CCACHE="/usr/bin/ccache"
          declare -A ARCH_MAP_FILE=(["x86_64"]="x86" ["arm64"]="arm64")
          echo "file_path=WSA-Linux-Kernel/arch/${ARCH_MAP_FILE[${{ inputs.arch }}]}/boot/${FILE_NAME[${{ inputs.arch }}]}" >> $GITHUB_ENV

      - name: Upload kernel-${{ inputs.arch }}-${{ inputs.version }}
        uses: actions/upload-artifact@v4
        with:
          name: kernel-WSA-${{ inputs.arch }}-${{ inputs.version }}
          path: "${{ env.file_path }}"
