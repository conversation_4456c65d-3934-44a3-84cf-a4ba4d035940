name: Build susfsd aarch64
on:
  push:
    branches: [ "next" ]
    paths:
      - '.github/workflows/susfsd.yml'
      - 'userspace/susfsd/**'
  workflow_dispatch:
  workflow_call:
    inputs:
      os:
        required: false
        type: string
        default: ubuntu-latest
jobs:
  build-susfs:
    name: Build userspace susfsd
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Build susfsd
      working-directory: ./userspace/susfsd
      run: $ANDROID_NDK/ndk-build
    - name: Upload Build Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: susfsd-linux-android
        path: ./userspace/susfsd/libs
    
