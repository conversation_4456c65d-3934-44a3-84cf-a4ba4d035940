name: Build LKM for KernelSU-Next
on:
  push:
    branches: [ "next" ]
    paths:
      - ".github/workflows/build-lkm.yml"
  pull_request:
    branches: [ "next" ]
    paths:
      - ".github/workflows/build-lkm.yml"
  workflow_call:
jobs:
  build-lkm:
    strategy:
      matrix:
        include:
          - version: "android12-5.10"
            sub_level: 236
            os_patch_level: 2025-05
          - version: "android13-5.10"
            sub_level: 236
            os_patch_level: 2025-05
          - version: "android13-5.15"
            sub_level: 180
            os_patch_level: 2025-05
          - version: "android14-5.15"
            sub_level: 180
            os_patch_level: 2025-05
          - version: "android14-6.1"
            sub_level: 138
            os_patch_level: 2025-06
          - version: "android15-6.6"
            sub_level: 89
            os_patch_level: 2025-06
    uses: ./.github/workflows/gki-kernel.yml
    with:
      version: ${{ matrix.version }}
      version_name: ${{ matrix.version }}.${{ matrix.sub_level }}
      tag: ${{ matrix.version }}-${{ matrix.os_patch_level }}
      os_patch_level: ${{ matrix.os_patch_level }}
      build_lkm: true