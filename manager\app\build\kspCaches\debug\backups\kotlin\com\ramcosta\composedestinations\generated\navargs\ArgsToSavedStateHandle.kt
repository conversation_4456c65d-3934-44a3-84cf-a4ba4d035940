package com.ramcosta.composedestinations.generated.navargs

import androidx.lifecycle.SavedStateHandle
import com.ramcosta.composedestinations.generated.destinations.AppProfileScreenDestinationNavArgs
import com.ramcosta.composedestinations.generated.destinations.ExecuteModuleActionScreenDestinationNavArgs
import com.ramcosta.composedestinations.generated.destinations.FlashScreenDestinationNavArgs
import com.ramcosta.composedestinations.generated.navtype.*
import com.ramcosta.composedestinations.generated.navtype.flashItNavType
import com.ramcosta.composedestinations.generated.navtype.superUserViewModelAppInfoNavType
import com.ramcosta.composedestinations.navargs.primitives.*
import com.ramcosta.composedestinations.navargs.primitives.array.*
import com.ramcosta.composedestinations.navargs.primitives.arraylist.*

public fun AppProfileScreenDestinationNavArgs.toSavedStateHandle(
    handle: SavedStateHandle = SavedStateHandle()
): SavedStateHandle {
	superUserViewModelAppInfoNavType.put(handle, "appInfo", appInfo)
    return handle
}

public fun ExecuteModuleActionScreenDestinationNavArgs.toSavedStateHandle(
    handle: SavedStateHandle = SavedStateHandle()
): SavedStateHandle {
	stringNavType.put(handle, "moduleId", moduleId)
    return handle
}

public fun FlashScreenDestinationNavArgs.toSavedStateHandle(
    handle: SavedStateHandle = SavedStateHandle()
): SavedStateHandle {
	flashItNavType.put(handle, "flashIt", flashIt)
	booleanNavType.put(handle, "finishIntent", finishIntent)
    return handle
}