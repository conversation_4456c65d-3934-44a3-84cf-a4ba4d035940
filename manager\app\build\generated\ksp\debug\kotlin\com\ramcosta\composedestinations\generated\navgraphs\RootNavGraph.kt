@file:OptIn(com.ramcosta.composedestinations.annotation.internal.InternalDestinationsApi::class)

package com.ramcosta.composedestinations.generated.navgraphs

import androidx.annotation.Keep
import com.ramcosta.composedestinations.animations.NavHostAnimatedDestinationStyle
import com.ramcosta.composedestinations.animations.defaults.NoTransitions
import com.ramcosta.composedestinations.annotation.RootGraph
import com.ramcosta.composedestinations.generated.destinations.*
import com.ramcosta.composedestinations.generated.destinations.HomeScreenDestination
import com.ramcosta.composedestinations.generated.navgraphs.*
import com.ramcosta.composedestinations.spec.*
import com.ramcosta.composedestinations.spec.DirectionNavHostGraphSpec
import com.rifsxd.ksunext.ui.screen.AppProfileScreen
import com.rifsxd.ksunext.ui.screen.ExecuteModuleActionScreen
import com.rifsxd.ksunext.ui.screen.FlashScreen
import com.rifsxd.ksunext.ui.screen.HomeScreen
import com.rifsxd.ksunext.ui.screen.InstallScreen
import com.rifsxd.ksunext.ui.screen.ModuleScreen
import com.rifsxd.ksunext.ui.screen.SuperUserScreen

/**
 * Generated from [RootGraph]
 *
 * * 🗺️[RootGraph]
 * * ∙∙∙∙∙∙∙∙↳📍🏁[HomeScreen]
 * * ∙∙∙∙∙∙∙∙↳📍[AppProfileScreen]
 * * ∙∙∙∙∙∙∙∙↳📍[ExecuteModuleActionScreen]
 * * ∙∙∙∙∙∙∙∙↳📍[FlashScreen]
 * * ∙∙∙∙∙∙∙∙↳📍[InstallScreen]
 * * ∙∙∙∙∙∙∙∙↳📍[ModuleScreen]
 * * ∙∙∙∙∙∙∙∙↳📍[SuperUserScreen]
 */
@Keep
public data object RootNavGraph : BaseRoute(), DirectionNavHostGraphSpec {
    
    override val startRoute: TypedRoute<Unit> = HomeScreenDestination

    override val defaultStartDirection: Direction = defaultStartDirection()
    
    override val destinations: List<DestinationSpec> get() = listOf(
		AppProfileScreenDestination,
		ExecuteModuleActionScreenDestination,
		FlashScreenDestination,
		HomeScreenDestination,
		InstallScreenDestination,
		ModuleScreenDestination,
		SuperUserScreenDestination
    )

	override val defaultTransitions: NavHostAnimatedDestinationStyle = NoTransitions
    
	override val route: String = "root"



}
