version: 2
updates:
  - package-ecosystem: github-actions
    directory: /
    schedule:
      interval: daily
    groups:
      actions:
        patterns:
          - "*"
  - package-ecosystem: cargo
    directory: userspace/ksud_magic
    schedule:
      interval: daily
    allow:
      - dependency-type: "all"
    groups:
      crates:
        patterns:
          - "*"
  - package-ecosystem: cargo
    directory: userspace/ksud_overlayfs
    schedule:
      interval: daily
    allow:
      - dependency-type: "all"
    groups:
      crates:
        patterns:
          - "*"
  - package-ecosystem: gradle
    directory: manager
    schedule:
      interval: daily
    groups:
      maven:
        patterns:
          - "*"