[package]
name = "ksud"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
anyhow = "1"
clap = { version = "4", features = ["derive"] }
const_format = "0.2"
zip = { version = "3",  features = [

    "deflate",
    "deflate64",
    "time",
    "lzma",
    "xz",
], default-features = false }
zip-extensions = { version = "0.8", features = [
    "deflate",
    "lzma",
    "xz",
], default-features = false }
java-properties = { git = "https://github.com/KernelSU-Next/java-properties.git", branch = "master", default-features = false }
log = "0.4"
env_logger = { version = "0.11", default-features = false }
serde_json = "1"
encoding_rs = "0.8"
humansize = "2"
libc = "0.2"
extattr = "1"
jwalk = "0.8"
is_executable = "1"
nom = "8"
derive-new = "0.7"
rust-embed = { version = "8", features = [
    "debug-embed",
    "compression", # must clean build after updating binaries
] }
which = "8"
getopts = "0.2"
sha256 = "1"
sha1 = "0.10"
tempfile = "3"
chrono = "0.4"
regex-lite = "0.1"

[target.'cfg(any(target_os = "android", target_os = "linux"))'.dependencies]
rustix = { git = "https://github.com/KernelSU-Next/rustix.git", branch = "main", features = [
    "all-apis",
] }
# some android specific dependencies which compiles under unix are also listed here for convenience of coding
android-properties = { version = "0.2", features = ["bionic-deprecated"] }
procfs = "0.17"

[target.'cfg(target_os = "android")'.dependencies]
android_logger = { version = "0.15", default-features = false }

[profile.release]
strip = true
opt-level = "z"
lto = true
codegen-units = 1